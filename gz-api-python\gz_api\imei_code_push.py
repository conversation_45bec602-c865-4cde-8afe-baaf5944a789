#!/usr/bin/env python3
"""
1.7 推送设备IMEI编码
当公证书状态是视频已通过时调用此接口推送设备IMEI编码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gz_api.utils import GzApiUtils
from gz_api.response_parser import ResponseParser

def imei_code_push_basic():
    """推送设备IMEI编码 - 基础版本"""
    # 构建请求参数
    params = {
        'gzOrderId': 'OI1970412769182351360',  # 订单号（三方流水号，唯一）
        'imeiCode': '355011479595868',         # 设备IMEI编码
        'isSecondHand': '2'                    # 是否是二手机：1-是，2-否
    }
    
    GzApiUtils.print_section("推送设备IMEI编码")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print(f"订单号: {params['gzOrderId']}")
    print(f"IMEI编码: {params['imeiCode']}")
    print(f"是否二手机: {'是' if params['isSecondHand'] == '1' else '否'}")
    print(f"请求参数: {params}")
    
    # 发送请求
    response_data = GzApiUtils.send_request('gzs/imeiCodePush', params)
    
    # 解析响应
    ResponseParser.parse_imei_push_response(response_data)

def imei_code_push_manual():
    """推送设备IMEI编码 - 手动输入版本"""
    GzApiUtils.print_section("推送设备IMEI编码 - 手动输入模式")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print("功能说明: 当公证书状态是视频已通过时推送设备IMEI编码")
    print()
    
    try:
        # 获取用户输入
        gz_order_id = GzApiUtils.get_input_with_default(
            "请输入订单号（三方流水号）", 
            "OI1970412769182351360"
        )
        
        # 获取有效的IMEI编码输入
        while True:
            imei_code = GzApiUtils.get_input_with_default(
                "请输入IMEI编码（15位数字）", 
                "355011479595868"
            )
            
            # 验证IMEI格式
            if GzApiUtils.validate_imei(imei_code):
                break
            else:
                GzApiUtils.print_error("错误：IMEI编码必须是15位数字，请重新输入！")
        
        # 获取是否二手机输入
        while True:
            is_second_hand = GzApiUtils.get_input_with_default(
                "是否是二手机？(1-是, 2-否)", 
                "2"
            )
            
            # 验证输入
            if is_second_hand in ['1', '2']:
                break
            else:
                GzApiUtils.print_error("错误：请输入1（是）或2（否），请重新输入！")
        
        # 构建请求参数
        params = {
            'gzOrderId': gz_order_id,
            'imeiCode': imei_code,
            'isSecondHand': is_second_hand
        }
        
        GzApiUtils.print_section("请求信息")
        print(f"订单号: {gz_order_id}")
        print(f"IMEI编码: {imei_code}")
        print(f"是否二手机: {'是' if is_second_hand == '1' else '否'}")
        print(f"请求参数: {params}")
        
        # 确认发送请求
        if GzApiUtils.confirm_action("\n是否发送请求？"):
            GzApiUtils.print_section("发送请求")
            
            # 发送请求
            response_data = GzApiUtils.send_request('gzs/imeiCodePush', params)
            
            # 解析响应
            ResponseParser.parse_imei_push_response(response_data)
        else:
            print("已取消请求发送")
            
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        GzApiUtils.print_error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "manual":
        imei_code_push_manual()
    else:
        imei_code_push_basic()
