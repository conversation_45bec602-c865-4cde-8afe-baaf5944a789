/**
 * 公证接口模态框组件
 * 提供公证相关功能的用户界面
 */

class NotarizationModal {
    constructor() {
        this.modal = null;
        this.currentTab = 'settle-order';
        this.isLoading = false;
        this.pendingRequests = new Set(); // 防止重复请求
        this.debounceTimers = new Map(); // 防抖计时器
        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
        this.loadUserPermissions();
    }

    createModal() {
        const modalHtml = `
            <div class="modal fade" id="notarizationModal" tabindex="-1" aria-labelledby="notarizationModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="notarizationModalLabel">
                                <i class="bi bi-shield-check me-2"></i>公证接口工具
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- 功能选项卡 -->
                            <ul class="nav nav-tabs mb-3" id="notarizationTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="settle-order-tab" data-bs-toggle="tab" 
                                            data-bs-target="#settle-order" type="button" role="tab">
                                        <i class="bi bi-check-circle me-1"></i>结清订单
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="query-orders-tab" data-bs-toggle="tab" 
                                            data-bs-target="#query-orders" type="button" role="tab">
                                        <i class="bi bi-search me-1"></i>查询订单
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="query-imei-tab" data-bs-toggle="tab" 
                                            data-bs-target="#query-imei" type="button" role="tab">
                                        <i class="bi bi-phone me-1"></i>查询IMEI
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="push-imei-tab" data-bs-toggle="tab" 
                                            data-bs-target="#push-imei" type="button" role="tab">
                                        <i class="bi bi-upload me-1"></i>推送IMEI
                                    </button>
                                </li>
                            </ul>

                            <!-- 选项卡内容 -->
                            <div class="tab-content" id="notarizationTabContent">
                                <!-- 结清订单 -->
                                <div class="tab-pane fade show active" id="settle-order" role="tabpanel">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning-light">
                                            <h6 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>结清公证订单</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-warning" role="alert">
                                                <strong>注意：</strong>结清操作不可逆，请确认订单号正确！
                                            </div>
                                            <form id="settleOrderForm">
                                                <div class="mb-3">
                                                    <label for="settleOrderId" class="form-label">订单号（三方流水号）</label>
                                                    <input type="text" class="form-control" id="settleOrderId" 
                                                           placeholder="请输入要结清的订单号" required>
                                                    <div class="form-text">请输入完整的订单号，例如：OI1970412769182351360</div>
                                                </div>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="bi bi-check-circle me-1"></i>结清订单
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- 查询订单 -->
                                <div class="tab-pane fade" id="query-orders" role="tabpanel">
                                    <div class="card border-info">
                                        <div class="card-header bg-info-light">
                                            <h6 class="mb-0"><i class="bi bi-search me-2"></i>查询用户有效订单</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="queryOrdersForm">
                                                <div class="mb-3">
                                                    <label for="queryIdCard" class="form-label">身份证号</label>
                                                    <input type="text" class="form-control" id="queryIdCard" 
                                                           placeholder="请输入18位身份证号" maxlength="18" required>
                                                    <div class="form-text">查询用户的有效公证订单数和在租台数</div>
                                                </div>
                                                <button type="submit" class="btn btn-info">
                                                    <i class="bi bi-search me-1"></i>查询订单
                                                </button>
                                            </form>
                                            <div id="queryOrdersResult" class="mt-3" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 查询IMEI -->
                                <div class="tab-pane fade" id="query-imei" role="tabpanel">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary-light">
                                            <h6 class="mb-0"><i class="bi bi-phone me-2"></i>查询IMEI编码有效性</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="queryImeiForm">
                                                <div class="mb-3">
                                                    <label for="queryImeiCodes" class="form-label">IMEI编码</label>
                                                    <textarea class="form-control" id="queryImeiCodes" rows="3" 
                                                              placeholder="请输入IMEI编码，多个用逗号分隔&#10;例如：123456789012345,355011479595868" required></textarea>
                                                    <div class="form-text">支持批量查询，每个IMEI必须是15位数字</div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-search me-1"></i>查询IMEI
                                                </button>
                                            </form>
                                            <div id="queryImeiResult" class="mt-3" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 推送IMEI -->
                                <div class="tab-pane fade" id="push-imei" role="tabpanel">
                                    <div class="card border-success">
                                        <div class="card-header bg-success-light">
                                            <h6 class="mb-0"><i class="bi bi-upload me-2"></i>推送设备IMEI编码</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-info" role="alert">
                                                <strong>说明：</strong>此功能在公证书状态为"视频已通过"时使用
                                            </div>
                                            <form id="pushImeiForm">
                                                <div class="mb-3">
                                                    <label for="pushOrderId" class="form-label">订单号</label>
                                                    <input type="text" class="form-control" id="pushOrderId" 
                                                           placeholder="请输入订单号" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="pushImeiCode" class="form-label">IMEI编码</label>
                                                    <input type="text" class="form-control" id="pushImeiCode" 
                                                           placeholder="请输入15位IMEI编码" maxlength="15" required>
                                                    <div class="form-text">IMEI编码必须是15位数字</div>
                                                </div>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-upload me-1"></i>推送IMEI
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="text-muted small me-auto">
                                <i class="bi bi-info-circle me-1"></i>
                                权限等级：<span id="userPermissionLevel">-</span>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = new bootstrap.Modal(document.getElementById('notarizationModal'));
    }

    bindEvents() {
        // 表单提交事件
        document.getElementById('settleOrderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSettleOrder();
        });

        document.getElementById('queryOrdersForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleQueryOrders();
        });

        document.getElementById('queryImeiForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleQueryImei();
        });

        document.getElementById('pushImeiForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePushImei();
        });

        // 选项卡切换事件
        document.querySelectorAll('#notarizationTabs button').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').substring(1);
                this.clearResults();
            });
        });

        // 输入验证
        this.bindInputValidation();
    }

    bindInputValidation() {
        // 身份证号验证
        document.getElementById('queryIdCard').addEventListener('input', (e) => {
            const value = e.target.value.replace(/[^\dXx]/g, '');
            e.target.value = value;
        });

        // IMEI验证
        document.getElementById('pushImeiCode').addEventListener('input', (e) => {
            const value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });
    }

    async loadUserPermissions() {
        try {
            const response = await fetch('/api/notarization/status');
            const result = await response.json();
            
            if (result.success) {
                const permissions = result.data.user_permissions;
                const level = result.data.current_user_level;
                
                document.getElementById('userPermissionLevel').textContent = 
                    level === 'full' ? '完全权限' : 
                    level === 'standard' ? '标准权限' : 
                    level === 'limited' ? '有限权限' : '未知';
                
                // 根据权限禁用功能
                this.updateUIBasedOnPermissions(permissions);
            }
        } catch (error) {
            console.error('加载用户权限失败:', error);
        }
    }

    updateUIBasedOnPermissions(permissions) {
        // 结清订单和推送IMEI需要标准权限
        if (!permissions.standard) {
            document.getElementById('settle-order-tab').classList.add('disabled');
            document.getElementById('push-imei-tab').classList.add('disabled');
            
            // 添加提示
            const settleCard = document.querySelector('#settle-order .card-body');
            const pushCard = document.querySelector('#push-imei .card-body');
            
            [settleCard, pushCard].forEach(card => {
                if (card) {
                    card.insertAdjacentHTML('afterbegin', 
                        '<div class="alert alert-warning">此功能需要标准权限或以上</div>'
                    );
                }
            });
        }
        
        // 查询功能需要有限权限（所有用户都有）
        if (!permissions.limited) {
            // 禁用所有功能
            document.querySelectorAll('#notarizationTabs button').forEach(tab => {
                tab.classList.add('disabled');
            });
        }
    }

    show() {
        this.modal.show();
    }

    hide() {
        this.modal.hide();
    }

    clearResults() {
        document.getElementById('queryOrdersResult').style.display = 'none';
        document.getElementById('queryImeiResult').style.display = 'none';
    }

    async handleSettleOrder() {
        const orderId = document.getElementById('settleOrderId').value.trim();

        if (!orderId) {
            this.showAlert('请输入订单号', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `settle-order-${orderId}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        // 二次确认
        if (!confirm(`确认要结清订单 ${orderId} 吗？此操作不可逆！`)) {
            return;
        }

        const finalConfirm = prompt('请输入 "CONFIRM" 确认结清操作：');
        if (finalConfirm !== 'CONFIRM') {
            this.showAlert('确认失败，已取消操作', 'info');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/settle-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(result.message || '订单结清成功', 'success');
                document.getElementById('settleOrderId').value = '';
            } else {
                this.showAlert(result.error || '结清失败', 'danger');
            }
        } catch (error) {
            console.error('结清订单失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
        }
    }

    async handleQueryOrders() {
        const idCard = document.getElementById('queryIdCard').value.trim();

        if (!idCard) {
            this.showAlert('请输入身份证号', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `query-orders-${idCard}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/query-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id_card: idCard })
            });

            const result = await response.json();

            if (result.success) {
                this.displayOrderQueryResult(result.data);
            } else {
                this.showAlert(result.error || '查询失败', 'danger');
            }
        } catch (error) {
            console.error('查询订单失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
        }
    }

    async handleQueryImei() {
        const imeiCodes = document.getElementById('queryImeiCodes').value.trim();

        if (!imeiCodes) {
            this.showAlert('请输入IMEI编码', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `query-imei-${imeiCodes}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/query-imei', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ imei_codes: imeiCodes })
            });

            const result = await response.json();

            if (result.success) {
                this.displayImeiQueryResult(result.data, result.invalid_imeis);
            } else {
                this.showAlert(result.error || '查询失败', 'danger');
            }
        } catch (error) {
            console.error('查询IMEI失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
        }
    }

    async handlePushImei() {
        const orderId = document.getElementById('pushOrderId').value.trim();
        const imeiCode = document.getElementById('pushImeiCode').value.trim();

        if (!orderId || !imeiCode) {
            this.showAlert('请填写完整信息', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `push-imei-${orderId}-${imeiCode}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/push-imei', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId, imei_code: imeiCode })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(result.message || 'IMEI推送成功', 'success');
                document.getElementById('pushOrderId').value = '';
                document.getElementById('pushImeiCode').value = '';
            } else {
                this.showAlert(result.error || '推送失败', 'danger');
            }
        } catch (error) {
            console.error('推送IMEI失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
        }
    }

    displayOrderQueryResult(data) {
        const resultDiv = document.getElementById('queryOrdersResult');
        const html = `
            <div class="card border-info">
                <div class="card-header bg-info-light">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>查询结果</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-file-text me-2 text-primary"></i>
                                <span>有效公证订单数：<strong>${data.certified_orders}</strong> 个</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-phone me-2 text-success"></i>
                                <span>在租台数：<strong>${data.rented_devices}</strong> 台</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        ${data.can_apply_more ?
                            `<div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                还可以申请 <strong>${data.remaining_slots}</strong> 台设备
                            </div>` :
                            `<div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                已达到最大设备数量限制（5台）
                            </div>`
                        }
                    </div>
                </div>
            </div>
        `;
        resultDiv.innerHTML = html;
        resultDiv.style.display = 'block';
    }

    displayImeiQueryResult(data, invalidImeis) {
        const resultDiv = document.getElementById('queryImeiResult');
        let html = `
            <div class="card border-primary">
                <div class="card-header bg-primary-light">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>查询结果</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-primary">${data.total_queried}</div>
                                <small class="text-muted">查询总数</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-success">${data.available_count}</div>
                                <small class="text-muted">可用数量</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-danger">${data.duplicate_count}</div>
                                <small class="text-muted">重复数量</small>
                            </div>
                        </div>
                    </div>
        `;

        if (data.all_available) {
            html += `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    所有IMEI编码都可用，可以继续申请公证
                </div>
            `;
        } else {
            html += `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    发现重复的IMEI编码，这些编码不可用于申请公证
                </div>
            `;
        }

        // 显示可用的IMEI
        if (data.available_imeis.length > 0) {
            html += `
                <div class="mt-3">
                    <h6 class="text-success"><i class="bi bi-check-circle me-2"></i>可用的IMEI编码：</h6>
                    <div class="d-flex flex-wrap gap-2">
                        ${data.available_imeis.map(imei =>
                            `<span class="badge bg-success">${imei}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        // 显示重复的IMEI
        if (data.duplicate_imeis.length > 0) {
            html += `
                <div class="mt-3">
                    <h6 class="text-danger"><i class="bi bi-x-circle me-2"></i>重复的IMEI编码：</h6>
                    <div class="d-flex flex-wrap gap-2">
                        ${data.duplicate_imeis.map(imei =>
                            `<span class="badge bg-danger">${imei}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        // 显示格式错误的IMEI
        if (invalidImeis && invalidImeis.length > 0) {
            html += `
                <div class="mt-3">
                    <h6 class="text-warning"><i class="bi bi-exclamation-triangle me-2"></i>格式错误的IMEI编码：</h6>
                    <div class="d-flex flex-wrap gap-2">
                        ${invalidImeis.map(imei =>
                            `<span class="badge bg-warning">${imei}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        html += `
                </div>
            </div>
        `;

        resultDiv.innerHTML = html;
        resultDiv.style.display = 'block';
    }

    showAlert(message, type = 'info') {
        // 创建临时提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到当前活动的选项卡
        const activeTab = document.querySelector('.tab-pane.active .card-body');
        if (activeTab) {
            activeTab.insertBefore(alertDiv, activeTab.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        const buttons = document.querySelectorAll('#notarizationModal button[type="submit"]');

        buttons.forEach(button => {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>处理中...';
            } else {
                button.disabled = false;
                // 恢复原始文本
                const icon = button.querySelector('i').className;
                const text = button.textContent.replace('处理中...', '').trim();
                button.innerHTML = `<i class="${icon} me-1"></i>${text}`;
            }
        });
    }
}

// 全局实例
window.NotarizationModal = NotarizationModal;

// 自动初始化 - 防止重复初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经初始化
    if (typeof window.notarizationModal === 'undefined') {
        try {
            window.notarizationModal = new NotarizationModal();
            console.log('公证模态框组件初始化成功');
        } catch (error) {
            console.error('公证模态框组件初始化失败:', error);
        }
    } else {
        console.log('公证模态框组件已存在，跳过重复初始化');
    }
});
