from setuptools import setup, find_packages

setup(
    name="gz-api-python",
    version="1.0.0",
    description="公证接口Python实现",
    author="GZ API Team",
    packages=find_packages(),
    install_requires=[
        "requests>=2.28.0",
        "colorama>=0.4.6",
    ],
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
