import os
from app import create_app
from app.services.scheduled_tasks import init_scheduler

# 获取当前工作目录
current_directory = os.getcwd()
print(f"Current working directory: {current_directory}")

# 创建Flask应用实例2222
app = create_app()

# 初始化定时任务调度器（避免在Debug重载时重复启动）
disable_scheduler = os.getenv('DISABLE_SCHEDULER', '0') == '1'
if not disable_scheduler and (os.environ.get('WERKZEUG_RUN_MAIN') == 'true' or not app.debug):
    with app.app_context():
        init_scheduler(app)
else:
    if disable_scheduler:
        print('Scheduler disabled by DISABLE_SCHEDULER=1')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
