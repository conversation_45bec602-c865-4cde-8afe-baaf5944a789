#!/usr/bin/env python3
"""
公证接口Python实现 - 主程序入口
提供交互式菜单选择不同的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gz_api.utils import GzApiUtils
from gz_api.early_settlement_order import early_settlement_order_basic, early_settlement_order_manual
from gz_api.all_running_order_query import all_running_order_query_basic, all_running_order_query_manual
from gz_api.query_valid_imei import query_valid_imei_basic, query_valid_imei_manual
from gz_api.imei_code_push import imei_code_push_basic, imei_code_push_manual

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🎯 公证接口Python实现 - 功能菜单")
    print("="*60)
    print("📋 商户信息:")
    print(f"   商户号: {GzApiUtils.MERCHANT_NO}")
    print(f"   服务器: {GzApiUtils.BASE_URL}")
    print()
    print("🚀 可用功能:")
    print("   1. 结清公证订单 (基础版本)")
    print("   2. 结清公证订单 (手动输入版本)")
    print("   3. 用户有效订单查询 (基础版本)")
    print("   4. 用户有效订单查询 (手动输入版本)")
    print("   5. 查询有效的IMEI编码 (基础版本)")
    print("   6. 查询有效的IMEI编码 (手动输入版本)")
    print("   7. 推送设备IMEI编码 (基础版本)")
    print("   8. 推送设备IMEI编码 (手动输入版本)")
    print("   0. 退出程序")
    print("="*60)

def main():
    """主程序"""
    try:
        while True:
            show_menu()
            
            try:
                choice = input("\n请选择功能 (0-8): ").strip()
                
                if choice == '0':
                    print("\n👋 感谢使用公证接口Python实现！")
                    break
                elif choice == '1':
                    early_settlement_order_basic()
                elif choice == '2':
                    early_settlement_order_manual()
                elif choice == '3':
                    all_running_order_query_basic()
                elif choice == '4':
                    all_running_order_query_manual()
                elif choice == '5':
                    query_valid_imei_basic()
                elif choice == '6':
                    query_valid_imei_manual()
                elif choice == '7':
                    imei_code_push_basic()
                elif choice == '8':
                    imei_code_push_manual()
                else:
                    GzApiUtils.print_error("无效选择，请输入0-8之间的数字")
                
                if choice != '0':
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序已退出")
                break
            except Exception as e:
                GzApiUtils.print_error(f"程序执行出错: {str(e)}")
                input("\n按回车键继续...")
                
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")

if __name__ == "__main__":
    main()
