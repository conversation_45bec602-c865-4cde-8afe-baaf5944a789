"""串码查重服务模块 V2

支持数据库适配器注入的新版本串码查重服务，
在 PostgreSQL 不可用时自动回退到本地 SQLite，确保功能可用。
"""

import logging
from typing import Dict, Any, Optional, List

from .database_adapter import (
    DatabaseAdapter,
    DatabaseError,
    DatabaseConnectionError,
)
from .config_manager import config_manager
from .postgresql_adapter import PostgreSQLAdapter

# 向后兼容：导入原验证函数
try:
    from .serial_number_service import validate_serial_number
except Exception:
    def validate_serial_number(serial_number: str) -> Dict[str, Any]:
        if not serial_number:
            return {'valid': False, 'error': '串码不能为空'}
        cleaned = ''.join(c for c in serial_number if c.isdigit())
        if len(cleaned) != 15:
            return {'valid': False, 'error': f'串码必须为15位数字，当前为{len(cleaned)}'}
        if not cleaned.isdigit():
            return {'valid': False, 'error': '串码只能包含数字'}
        return {'valid': True, 'cleaned_serial': cleaned}


logger = logging.getLogger(__name__)


class SerialNumberServiceV2:
    """串码查重服务 V2（支持适配器注入，PG/SQLite）"""

    def __init__(self, db_adapter: Optional[DatabaseAdapter] = None):
        self.db_adapter = db_adapter or self._create_default_adapter()
        logger.info(f"串码查重服务V2已初始化，使用适配器: {type(self.db_adapter).__name__}")

    def _create_default_adapter(self) -> DatabaseAdapter:
        """根据配置创建默认适配器；PG失败时自动回退到SQLite。"""
        db_config = config_manager.get_database_config()

        if db_config.database_type == 'postgresql':
            adapter = PostgreSQLAdapter(db_config)
            logger.info("使用PostgreSQL适配器")
            try:
                if adapter.connect():
                    return adapter
            except DatabaseConnectionError as e:
                logger.warning(f"PostgreSQL连接失败，将回退到SQLite：{e}")
            except Exception as e:
                logger.warning(f"PostgreSQL连接异常，将回退到SQLite：{e}")

        # 回退到原始SQLite实现
        from .serial_number_service import get_sqlite_service

        class SQLiteAdapterWrapper(DatabaseAdapter):
            def __init__(self):
                self.service = get_sqlite_service()

            def connect(self) -> bool:
                return True

            def disconnect(self) -> None:
                pass

            def is_connected(self) -> bool:
                return True

            def create_tables(self) -> None:
                pass

            def create_indexes(self) -> None:
                pass

            def check_duplicate(self, serial_number: str) -> bool:
                return self.service.check_duplicate(serial_number)

            def add_serial_number(self, serial_number: str,
                                  created_by: Optional[str] = None,
                                  notes: Optional[str] = None) -> Dict[str, Any]:
                return self.service.add_serial_number(serial_number, created_by, notes)

            def get_serial_number_info(self, serial_number: str) -> Optional[Dict[str, Any]]:
                return self.service.get_serial_number_info(serial_number)

            def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
                return self.service.get_recent_records(limit)

            def get_statistics(self) -> Dict[str, Any]:
                return self.service.get_statistics()

            def get_record_count(self) -> int:
                stats = self.service.get_statistics()
                return stats.get('total_count', 0)

            def export_all_data(self) -> List[Dict[str, Any]]:
                return self.get_recent_records(10000)

            def import_data(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
                imported_count = 0
                failed_count = 0
                errors: List[str] = []
                for rec in records:
                    try:
                        res = self.service.add_serial_number(
                            rec.get('serial_number'),
                            rec.get('created_by'),
                            rec.get('notes')
                        )
                        if res.get('success'):
                            imported_count += 1
                        else:
                            failed_count += 1
                            errors.append(res.get('message', 'Unknown error'))
                    except Exception as e:
                        failed_count += 1
                        errors.append(str(e))
                return {
                    'success': True,
                    'imported_count': imported_count,
                    'failed_count': failed_count,
                    'errors': errors,
                }

            def validate_schema(self) -> bool:
                return True

            def get_adapter_info(self) -> Dict[str, str]:
                return {'adapter_type': 'SQLiteAdapterWrapper'}

        logger.info("使用SQLite适配器包装器")
        return SQLiteAdapterWrapper()

    def check_duplicate(self, serial_number: str) -> bool:
        try:
            return self.db_adapter.check_duplicate(serial_number)
        except DatabaseError as e:
            logger.error(f"检查串码重复时发生错误: {e}")
            raise

    def check_serial_number(self, serial_number: str) -> Dict[str, Any]:
        try:
            validation = validate_serial_number(serial_number)
            if not validation['valid']:
                return {'success': False, 'error': validation['error']}

            cleaned = validation['cleaned_serial']
            record = self.db_adapter.get_serial_number_info(cleaned)
            return {
                'success': True,
                'exists': record is not None,
                'record': record,
                'serial_number': cleaned,
            }
        except DatabaseError as e:
            logger.error(f"检查串码时发生错误: {e}")
            return {'success': False, 'error': f'检查串码时发生错误: {str(e)}'}

    def add_serial_number(self, serial_number: str,
                          created_by: Optional[str] = None,
                          notes: Optional[str] = None) -> Dict[str, Any]:
        try:
            return self.db_adapter.add_serial_number(serial_number, created_by, notes)
        except DatabaseError as e:
            logger.error(f"添加串码时发生错误: {e}")
            return {'success': False, 'error': '系统错误', 'message': f'添加串码时发生错误: {str(e)}'}

    def get_serial_number_info(self, serial_number: str) -> Optional[Dict[str, Any]]:
        try:
            return self.db_adapter.get_serial_number_info(serial_number)
        except DatabaseError as e:
            logger.error(f"获取串码信息时发生错误: {e}")
            raise

    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        try:
            return self.db_adapter.get_recent_records(limit)
        except DatabaseError as e:
            logger.error(f"获取最近记录时发生错误: {e}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        try:
            return self.db_adapter.get_statistics()
        except DatabaseError as e:
            logger.error(f"获取统计信息时发生错误: {e}")
            return {
                'total_count': 0,
                'today_count': 0,
                'week_count': 0,
                'last_updated': '',
                'error': str(e),
            }

    def get_adapter_info(self) -> Dict[str, Any]:
        try:
            adapter_info = getattr(self.db_adapter, 'get_adapter_info', lambda: {'adapter_type': type(self.db_adapter).__name__})()
            db_config = config_manager.get_database_config()
            return {
                'adapter_type': adapter_info.get('adapter_type', type(self.db_adapter).__name__),
                'database_type': db_config.database_type,
                'connection_info': db_config.get_safe_connection_string(),
                'is_connected': self.db_adapter.is_connected(),
            }
        except Exception as e:
            return {'error': str(e)}


# 全局实例（懒加载，避免在导入阶段触发任何数据库连接）
serial_service_v2 = None


def get_serial_service() -> SerialNumberServiceV2:
    """获取串码服务实例（懒加载）。"""
    global serial_service_v2
    if serial_service_v2 is None:
        serial_service_v2 = SerialNumberServiceV2()
    return serial_service_v2
