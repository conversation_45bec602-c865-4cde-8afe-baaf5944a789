# 🐍 公证接口Python版本详细使用指南

## 🎯 项目概述

这是公证接口的完整Python实现，与Java版本功能完全一致，提供了更友好的用户体验和更简单的部署方式。

## 📦 安装和配置

### 1. 环境准备
```bash
# 检查Python版本（需要3.7+）
python --version

# 创建虚拟环境（推荐）
python -m venv gz-api-env
source gz-api-env/bin/activate  # Linux/Mac
# 或
gz-api-env\Scripts\activate     # Windows
```

### 2. 安装依赖
```bash
# 进入项目目录
cd gz-api-python

# 安装依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .
```

### 3. 验证安装
```bash
# 测试导入
python -c "from gz_api.utils import GzApiUtils; print('安装成功！')"
```

## 🚀 运行方式详解

### 方式一：交互式菜单（推荐新手）
```bash
python main.py
```

**优点**：
- 图形化菜单选择
- 无需记忆命令参数
- 适合探索和学习

**使用场景**：
- 首次使用
- 不确定要使用哪个功能
- 需要连续测试多个功能

### 方式二：快速启动（推荐熟练用户）
```bash
# 基本语法
python run.py [功能名称]

# 具体示例
python run.py settlement-manual      # 结清订单（手动输入）
python run.py order-query           # 订单查询（基础版本）
python run.py imei-query-manual     # IMEI查询（手动输入）
python run.py imei-push             # IMEI推送（基础版本）
```

**优点**：
- 快速启动
- 适合脚本化
- 命令行友好

**使用场景**：
- 熟悉功能后的日常使用
- 脚本自动化
- CI/CD集成

### 方式三：模块直接运行（推荐开发者）
```bash
# 基本语法
python -m gz_api.[模块名] [manual]

# 具体示例
python -m gz_api.early_settlement_order manual
python -m gz_api.all_running_order_query
python -m gz_api.query_valid_imei manual
python -m gz_api.imei_code_push
```

**优点**：
- 直接访问模块
- 适合调试
- 灵活性最高

**使用场景**：
- 开发和调试
- 集成到其他Python项目
- 自定义扩展

## 📋 功能详细说明

### 1.9 结清公证订单

#### 基础版本
```bash
python run.py settlement
```
- 使用预设订单号：`OI1970412769182351360`
- 自动执行结清操作
- 适合测试和演示

#### 手动输入版本
```bash
python run.py settlement-manual
```
- 交互式输入订单号
- **二次确认机制**：需要输入`CONFIRM`
- 详细的安全提示
- 适合生产环境使用

**安全特性**：
```
确认要结清订单 OI1970412769182351360 吗？ (y/n): y
请再次确认结清操作 (输入 'CONFIRM' 继续): CONFIRM
```

### 1.10 用户有效订单查询

#### 基础版本
```bash
python run.py order-query
```
- 使用预设身份证号：`350521199812164511`
- 自动查询用户订单信息

#### 手动输入版本
```bash
python run.py order-query-manual
```
- 交互式输入身份证号
- 自动验证身份证号格式（18位，最后一位可以是X）
- 详细的业务规则分析

**输出示例**：
```
📊 用户订单信息:
   有效公证订单数: 0 个
   在租台数: 0 台

🔍 业务状态分析:
   ✅ 用户当前没有在租设备
   ✅ 可以申请新的公证服务
   📝 用户当前没有有效的公证订单
```

### 1.11 查询有效的IMEI编码

#### 基础版本
```bash
python run.py imei-query
```
- 使用预设IMEI：`123456789012345,355011479595868`
- 批量查询多个IMEI编码

#### 手动输入版本
```bash
python run.py imei-query-manual
```
- 支持输入多个IMEI（逗号分隔）
- 自动验证IMEI格式（15位数字）
- 智能解析和格式检查

**输入示例**：
```
请输入要查询的IMEI编码（支持多个，用逗号分隔）:
IMEI编码 [默认: 123456789012345,355011479595868]: 355011479595868,123456789012345,invalid
✅ 355011479595868 - 格式正确
✅ 123456789012345 - 格式正确
❌ invalid - 格式错误（需要15位数字）
```

### 1.7 推送设备IMEI编码

#### 基础版本
```bash
python run.py imei-push
```
- 使用预设参数进行推送
- 订单号：`OI1970412769182351360`
- IMEI：`355011479595868`
- 非二手机

#### 手动输入版本
```bash
python run.py imei-push-manual
```
- 交互式输入所有参数
- 验证IMEI格式
- 二手机选择验证

**参数说明**：
- `gzOrderId`: 订单号（三方流水号，唯一）
- `imeiCode`: 设备IMEI编码（15位数字）
- `isSecondHand`: 是否二手机（1-是，2-否）

## 🎨 输出格式说明

### 彩色输出
- 🟢 **绿色**：成功信息（✅）
- 🔴 **红色**：错误信息（❌）
- 🟡 **黄色**：警告信息（⚠️）
- 🔵 **蓝色**：章节标题（===）
- 🟦 **青色**：信息提示（📋）

### 图标含义
- ✅ 成功/可用
- ❌ 失败/不可用
- ⚠️ 警告/注意
- 📋 信息/状态
- 📊 数据/统计
- 🔍 分析/验证
- 📱 IMEI相关
- 🎉 完成/结果
- 💡 建议/提示
- 📝 说明/备注

## 🔧 高级使用

### 1. 批量操作
```bash
# 创建批量脚本
cat > batch_test.sh << 'EOF'
#!/bin/bash
echo "开始批量测试..."
python run.py order-query
echo "---"
python run.py imei-query
echo "---"
python run.py imei-push
echo "批量测试完成"
EOF

chmod +x batch_test.sh
./batch_test.sh
```

### 2. 集成到其他Python项目
```python
# 在其他Python项目中使用
import sys
sys.path.append('/path/to/gz-api-python')

from gz_api.utils import GzApiUtils
from gz_api.response_parser import ResponseParser

# 发送请求
params = {'applicantID': '350521199812164511'}
response = GzApiUtils.send_request('gzs/allRunningOrderQuery', params)

# 解析响应
ResponseParser.parse_order_query_response(response)
```

### 3. 自定义配置
```python
# 修改配置
from gz_api.utils import GzApiUtils

# 临时修改配置
GzApiUtils.MERCHANT_NO = "你的商户号"
GzApiUtils.SECRET_KEY = "你的密钥"
GzApiUtils.BASE_URL = "你的服务器地址"
```

## 🐛 故障排除

### 常见问题

#### 1. 导入错误
```
ModuleNotFoundError: No module named 'gz_api'
```
**解决方案**：
```bash
# 确保在正确目录
cd gz-api-python

# 重新安装依赖
pip install -e .
```

#### 2. 网络连接错误
```
requests.exceptions.ConnectionError
```
**解决方案**：
- 检查网络连接
- 确认服务器地址正确
- 检查防火墙设置

#### 3. 彩色输出不显示
```bash
# Windows用户可能需要启用ANSI支持
pip install colorama
```

#### 4. 权限错误
```bash
# Linux/Mac用户可能需要执行权限
chmod +x main.py run.py
```

### 调试模式
```python
# 在代码中添加调试信息
import logging
logging.basicConfig(level=logging.DEBUG)

# 或者使用print调试
print(f"Debug: params = {params}")
print(f"Debug: response = {response}")
```

## 📊 性能优化

### 1. 请求超时设置
```python
# 在utils.py中修改超时时间
response = requests.post(
    url=url,
    json=params,
    headers=headers,
    timeout=60  # 增加到60秒
)
```

### 2. 连接池优化
```python
# 使用Session进行连接复用
import requests

session = requests.Session()
session.headers.update(headers)
response = session.post(url, json=params)
```

## 🔒 安全注意事项

### 1. 密钥保护
- 不要在代码中硬编码密钥
- 使用环境变量存储敏感信息
- 定期更换密钥

### 2. 输入验证
- 所有用户输入都经过格式验证
- 防止SQL注入和XSS攻击
- 限制输入长度和字符集

### 3. 网络安全
- 使用HTTPS传输（如果服务器支持）
- 验证服务器证书
- 实现请求重试和熔断机制

## 🎯 最佳实践

### 1. 开发建议
- 使用虚拟环境隔离依赖
- 遵循PEP 8编码规范
- 添加类型注解提高代码质量
- 编写单元测试

### 2. 生产部署
- 使用配置文件管理环境变量
- 实现日志记录和监控
- 设置合理的超时和重试机制
- 定期更新依赖库

### 3. 测试策略
- 先使用基础版本验证接口可用性
- 再使用手动输入版本测试各种参数
- 测试异常情况和边界条件
- 验证响应格式和业务逻辑

## 🎉 总结

Python版本的公证接口实现提供了：

- 🎯 **完整功能**：与Java版本100%功能一致
- 🎯 **更好体验**：彩色输出、友好提示
- 🎯 **简单部署**：仅需Python环境
- 🎯 **灵活使用**：多种运行方式
- 🎯 **安全可靠**：完整的验证和错误处理

推荐从交互式菜单开始，熟悉后使用快速启动方式提高效率！🚀
