"""数据库适配器模块

提供数据库操作的抽象接口，支持多种数据库后端的统一访问。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class DatabaseError(Exception):
    """数据库操作基础异常类"""
    pass


class DatabaseConnectionError(DatabaseError):
    """数据库连接异常"""
    pass


class DatabaseOperationError(DatabaseError):
    """数据库操作异常"""
    pass


class DatabaseConfigurationError(DatabaseError):
    """数据库配置异常"""
    pass


class MigrationError(DatabaseError):
    """数据迁移异常"""
    pass


class DatabaseAdapter(ABC):
    """数据库适配器抽象基类
    
    定义了串码查重服务所需的所有数据库操作接口。
    具体的数据库实现（如SQLite、PostgreSQL）需要继承此类并实现所有抽象方法。
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """建立数据库连接
        
        Returns:
            bool: 连接是否成功
            
        Raises:
            DatabaseConnectionError: 连接失败时抛出
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """关闭数据库连接"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查数据库连接状态
        
        Returns:
            bool: 是否已连接
        """
        pass
    
    @abstractmethod
    def create_tables(self) -> None:
        """创建数据库表结构
        
        Raises:
            DatabaseOperationError: 创建表失败时抛出
        """
        pass
    
    @abstractmethod
    def create_indexes(self) -> None:
        """创建数据库索引
        
        Raises:
            DatabaseOperationError: 创建索引失败时抛出
        """
        pass
    
    @abstractmethod
    def check_duplicate(self, serial_number: str) -> bool:
        """检查串码是否重复
        
        Args:
            serial_number: 15位串码
            
        Returns:
            bool: True表示已存在（重复），False表示不存在
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def add_serial_number(self, serial_number: str, 
                          created_by: Optional[str] = None, 
                          notes: Optional[str] = None) -> Dict[str, Any]:
        """添加新串码
        
        Args:
            serial_number: 15位串码
            created_by: 创建者
            notes: 备注信息
            
        Returns:
            Dict: 包含操作结果的字典，格式：
                {
                    'success': bool,
                    'message': str,
                    'record_id': int,  # 成功时包含
                    'error': str,      # 失败时包含
                    'serial_number': str,
                    'created_at': str
                }
                
        Raises:
            DatabaseOperationError: 插入失败时抛出
        """
        pass
    
    @abstractmethod
    def get_serial_number_info(self, 
                               serial_number: str) -> Optional[Dict[str, Any]]:
        """获取串码详细信息
        
        Args:
            serial_number: 15位串码
            
        Returns:
            Optional[Dict]: 串码信息，如果不存在则返回None，格式：
                {
                    'id': int,
                    'serial_number': str,
                    'created_at': str,
                    'created_by': str,
                    'notes': str
                }
                
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的串码记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 最近的串码记录列表，每个记录格式同get_serial_number_info
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """获取串码统计信息
        
        Returns:
            Dict: 统计信息，格式：
                {
                    'total_count': int,      # 总记录数
                    'today_count': int,      # 今日新增
                    'week_count': int,       # 本周新增
                    'last_updated': str      # 最后更新时间
                }
                
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def get_record_count(self) -> int:
        """获取总记录数
        
        Returns:
            int: 总记录数
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def export_all_data(self) -> List[Dict[str, Any]]:
        """导出所有数据
        
        Returns:
            List[Dict]: 所有串码记录列表
            
        Raises:
            DatabaseOperationError: 导出失败时抛出
        """
        pass
    
    @abstractmethod
    def import_data(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量导入数据
        
        Args:
            records: 要导入的记录列表
            
        Returns:
            Dict: 导入结果，格式：
                {
                    'success': bool,
                    'imported_count': int,
                    'failed_count': int,
                    'errors': List[str]
                }
                
        Raises:
            DatabaseOperationError: 导入失败时抛出
        """
        pass
    
    @abstractmethod
    def validate_schema(self) -> bool:
        """验证数据库表结构
        
        Returns:
            bool: 表结构是否正确
            
        Raises:
            DatabaseOperationError: 验证失败时抛出
        """
        pass
    
    def get_adapter_info(self) -> Dict[str, str]:
        """获取适配器信息
        
        Returns:
            Dict: 适配器信息
        """
        return {
            'adapter_type': self.__class__.__name__,
            'version': '1.0.0',
            'description': '串码查重数据库适配器'
        }