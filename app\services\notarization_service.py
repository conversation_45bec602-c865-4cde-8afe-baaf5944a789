"""
公证接口服务层
提供公证相关功能的Flask服务封装
"""

import hashlib
import json
import re
import time
import logging
from typing import Dict, Any, Optional, List
import requests
from flask import current_app
from flask_login import current_user

from app import cache

logger = logging.getLogger(__name__)


class NotarizationApiUtils:
    """公证接口工具类 - 适配Flask应用"""
    
    # 商户配置 - 可以从配置文件读取
    MERCHANT_NO = "NJSJ1738988721355510"
    SECRET_KEY = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk"
    BASE_URL = "http://buss.haochengda.net:8088"
    
    # 正则表达式
    IMEI_PATTERN = re.compile(r'^\d{15}$')
    ID_CARD_PATTERN = re.compile(r'^\d{17}[\dXx]$')
    
    @staticmethod
    def generate_signature(params: Dict[str, Any], secret_key: str) -> str:
        """
        生成MD5签名 - 与Java版本完全一致的算法
        
        Args:
            params: 请求参数字典
            secret_key: 密钥
            
        Returns:
            MD5签名字符串（大写）
        """
        # 按键名排序（与Java TreeMap一致）
        sorted_params = sorted(params.items())
        
        # 构建签名字符串
        digest = ""
        for key, value in sorted_params:
            if isinstance(value, list):
                # 处理数组参数 - 与Java版本保持一致
                value_str = json.dumps(value, separators=(',', ':'), ensure_ascii=False)
            else:
                value_str = str(value)
            digest += f"{key}={value_str}&"
        
        # 添加密钥
        digest += f"key={secret_key}"
        
        # 记录调试信息
        logger.debug(f"签名明文摘要：{digest}")
        
        # 生成MD5并转大写（与Java版本一致）
        md5_hash = hashlib.md5(digest.encode('utf-8')).hexdigest().upper()
        logger.debug(f"MD5签名结果: {md5_hash}")
        
        return md5_hash
    
    @staticmethod
    def send_request(endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            endpoint: 接口端点
            params: 请求参数
            
        Returns:
            响应结果字典
        """
        try:
            # 生成签名
            signature = NotarizationApiUtils.generate_signature(params, NotarizationApiUtils.SECRET_KEY)
            params['signature'] = signature
            
            # 构建请求URL
            url = f"{NotarizationApiUtils.BASE_URL}/{endpoint}"
            
            # 请求头
            headers = {
                'GZ-Merchant-No': NotarizationApiUtils.MERCHANT_NO,
                'GZ-Req-Timestamp': str(int(time.time() * 1000)),
                'Content-Type': 'application/json'
            }
            
            logger.info(f"发送公证API请求: {endpoint}")
            logger.debug(f"请求参数: {params}")
            
            # 发送POST请求
            response = requests.post(
                url=url,
                json=params,
                headers=headers,
                timeout=30
            )
            
            # 解析响应
            response_text = response.text
            response_json = None
            
            if response_text.strip().startswith('{'):
                try:
                    response_json = response.json()
                except json.JSONDecodeError:
                    logger.warning(f"响应JSON解析失败: {response_text}")
            
            result = {
                'success': True,
                'status_code': response.status_code,
                'response_text': response_text,
                'response_json': response_json
            }
            
            logger.info(f"公证API请求完成: {endpoint}, 状态码: {response.status_code}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"公证API请求失败: {endpoint}, 错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'response_text': None,
                'response_json': None
            }
    
    @staticmethod
    def validate_imei(imei: str) -> bool:
        """验证IMEI格式"""
        return bool(NotarizationApiUtils.IMEI_PATTERN.match(imei))
    
    @staticmethod
    def validate_id_card(id_card: str) -> bool:
        """验证身份证号格式"""
        return bool(NotarizationApiUtils.ID_CARD_PATTERN.match(id_card))
    
    @staticmethod
    def parse_imei_list(imei_input: str) -> List[str]:
        """解析IMEI列表输入"""
        imei_list = [imei.strip() for imei in imei_input.split(',')]
        valid_imeis = []
        invalid_imeis = []
        
        for imei in imei_list:
            if NotarizationApiUtils.validate_imei(imei):
                valid_imeis.append(imei)
            else:
                invalid_imeis.append(imei)
        
        return valid_imeis, invalid_imeis


class NotarizationService:
    """公证服务类 - 提供高级业务逻辑"""

    def __init__(self):
        self.api_utils = NotarizationApiUtils()
        self._request_cache = {}  # 简单的请求缓存，防止短时间内重复请求
    
    def check_user_permission(self, required_level: str = 'standard') -> bool:
        """检查用户权限"""
        if not current_user.is_authenticated:
            return False
        
        return current_user.has_permission(required_level)
    
    def settle_order(self, order_id: str) -> Dict[str, Any]:
        """
        结清公证订单
        
        Args:
            order_id: 订单号（三方流水号）
            
        Returns:
            处理结果
        """
        if not self.check_user_permission('standard'):
            return {
                'success': False,
                'error': '权限不足，需要标准权限或以上'
            }
        
        if not order_id or not order_id.strip():
            return {
                'success': False,
                'error': '订单号不能为空'
            }
        
        params = {'gzOrderId': order_id.strip()}
        
        try:
            response_data = self.api_utils.send_request('gzs/earlySettlementOrder', params)
            return self._parse_settlement_response(response_data)
        except Exception as e:
            logger.error(f"结清订单失败: {str(e)}")
            return {
                'success': False,
                'error': f'系统错误: {str(e)}'
            }
    
    def query_user_orders(self, id_card: str) -> Dict[str, Any]:
        """
        查询用户有效订单
        
        Args:
            id_card: 身份证号
            
        Returns:
            查询结果
        """
        if not self.check_user_permission('limited'):
            return {
                'success': False,
                'error': '权限不足'
            }
        
        if not id_card or not id_card.strip():
            return {
                'success': False,
                'error': '身份证号不能为空'
            }
        
        if not self.api_utils.validate_id_card(id_card.strip()):
            return {
                'success': False,
                'error': '身份证号格式不正确'
            }
        
        params = {'idCard': id_card.strip()}
        
        try:
            response_data = self.api_utils.send_request('gzs/allRunningOrderQuery', params)
            return self._parse_order_query_response(response_data)
        except Exception as e:
            logger.error(f"查询用户订单失败: {str(e)}")
            return {
                'success': False,
                'error': f'系统错误: {str(e)}'
            }
    
    def query_imei_validity(self, imei_codes: List[str]) -> Dict[str, Any]:
        """
        查询IMEI编码有效性
        
        Args:
            imei_codes: IMEI编码列表
            
        Returns:
            查询结果
        """
        if not self.check_user_permission('limited'):
            return {
                'success': False,
                'error': '权限不足'
            }
        
        if not imei_codes:
            return {
                'success': False,
                'error': 'IMEI编码列表不能为空'
            }
        
        # 验证IMEI格式
        valid_imeis = []
        invalid_imeis = []
        
        for imei in imei_codes:
            if self.api_utils.validate_imei(imei):
                valid_imeis.append(imei)
            else:
                invalid_imeis.append(imei)
        
        if not valid_imeis:
            return {
                'success': False,
                'error': '没有有效的IMEI编码',
                'invalid_imeis': invalid_imeis
            }
        
        params = {'imeiCodes': valid_imeis}
        
        try:
            response_data = self.api_utils.send_request('gzs/allImeiCodeQuery', params)
            result = self._parse_imei_query_response(response_data, valid_imeis)
            result['invalid_imeis'] = invalid_imeis
            return result
        except Exception as e:
            logger.error(f"查询IMEI有效性失败: {str(e)}")
            return {
                'success': False,
                'error': f'系统错误: {str(e)}'
            }

    def push_imei_code(self, order_id: str, imei_code: str) -> Dict[str, Any]:
        """
        推送设备IMEI编码

        Args:
            order_id: 订单号
            imei_code: IMEI编码

        Returns:
            推送结果
        """
        if not self.check_user_permission('standard'):
            return {
                'success': False,
                'error': '权限不足，需要标准权限或以上'
            }

        if not order_id or not order_id.strip():
            return {
                'success': False,
                'error': '订单号不能为空'
            }

        if not imei_code or not imei_code.strip():
            return {
                'success': False,
                'error': 'IMEI编码不能为空'
            }

        if not self.api_utils.validate_imei(imei_code.strip()):
            return {
                'success': False,
                'error': 'IMEI编码格式不正确'
            }

        params = {
            'gzOrderId': order_id.strip(),
            'imeiCode': imei_code.strip()
        }

        try:
            response_data = self.api_utils.send_request('gzs/imeiCodePush', params)
            return self._parse_imei_push_response(response_data)
        except Exception as e:
            logger.error(f"推送IMEI编码失败: {str(e)}")
            return {
                'success': False,
                'error': f'系统错误: {str(e)}'
            }

    def _parse_settlement_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析结清订单响应"""
        if not response_data.get('success'):
            return {
                'success': False,
                'error': response_data.get('error', '请求失败')
            }

        response_json = response_data.get('response_json')
        if not response_json:
            return {
                'success': False,
                'error': '响应格式错误或接口不可用'
            }

        code = response_json.get('code')
        msg = response_json.get('msg', '')
        success = response_json.get('success', False)

        if code == 0 and success:
            return {
                'success': True,
                'message': '公证订单结清成功',
                'details': '订单状态已更新为结清状态'
            }
        else:
            return {
                'success': False,
                'error': f'结清失败: {msg}',
                'code': code
            }

    def _parse_order_query_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析用户订单查询响应"""
        if not response_data.get('success'):
            return {
                'success': False,
                'error': response_data.get('error', '请求失败')
            }

        response_json = response_data.get('response_json')
        if not response_json:
            return {
                'success': False,
                'error': '响应格式错误或接口不可用'
            }

        code = response_json.get('code')
        success = response_json.get('success', False)

        if code == 0 and success:
            data = response_json.get('data', {})
            is_certified = data.get('isCertified', 0)
            goods_num = data.get('goodsNum', 0)

            return {
                'success': True,
                'data': {
                    'certified_orders': is_certified,
                    'rented_devices': goods_num,
                    'can_apply_more': goods_num < 5,
                    'remaining_slots': max(0, 5 - goods_num)
                },
                'message': '查询成功'
            }
        else:
            return {
                'success': False,
                'error': f'查询失败: {response_json.get("msg", "未知错误")}',
                'code': code
            }

    def _parse_imei_query_response(self, response_data: Dict[str, Any], queried_imeis: List[str]) -> Dict[str, Any]:
        """解析IMEI查询响应"""
        if not response_data.get('success'):
            return {
                'success': False,
                'error': response_data.get('error', '请求失败')
            }

        response_json = response_data.get('response_json')
        if not response_json:
            return {
                'success': False,
                'error': '响应格式错误或接口不可用'
            }

        code = response_json.get('code')
        success = response_json.get('success', False)

        if code == 0 and success:
            data = response_json.get('data', {})
            duplicate_imeis = data.get('imeiCodes', [])

            available_imeis = [imei for imei in queried_imeis if imei not in duplicate_imeis]

            return {
                'success': True,
                'data': {
                    'total_queried': len(queried_imeis),
                    'duplicate_count': len(duplicate_imeis),
                    'available_count': len(available_imeis),
                    'duplicate_imeis': duplicate_imeis,
                    'available_imeis': available_imeis,
                    'all_available': len(duplicate_imeis) == 0
                },
                'message': '查询成功'
            }
        else:
            return {
                'success': False,
                'error': f'查询失败: {response_json.get("msg", "未知错误")}',
                'code': code
            }

    def _parse_imei_push_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析IMEI推送响应"""
        if not response_data.get('success'):
            return {
                'success': False,
                'error': response_data.get('error', '请求失败')
            }

        response_json = response_data.get('response_json')
        if not response_json:
            return {
                'success': False,
                'error': '响应格式错误或接口不可用'
            }

        code = response_json.get('code')
        msg = response_json.get('msg', '')
        success = response_json.get('success', False)

        if code == 0 and success:
            return {
                'success': True,
                'message': 'IMEI编码推送成功',
                'details': '设备信息已记录到公证系统'
            }
        else:
            return {
                'success': False,
                'error': f'推送失败: {msg}',
                'code': code
            }


# 全局服务实例
_notarization_service = None

def get_notarization_service() -> NotarizationService:
    """获取公证服务实例（单例模式）"""
    global _notarization_service
    if _notarization_service is None:
        _notarization_service = NotarizationService()
    return _notarization_service
