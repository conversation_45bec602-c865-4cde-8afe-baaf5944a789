# 🐍 公证接口Python实现

完整的公证接口Python版本实现，提供与Java版本相同的功能特性。

## 📋 功能概览

### ✅ 已实现功能
1. **1.9 结清公证订单** - 结清指定的公证订单
2. **1.10 用户有效订单查询** - 查询用户的有效公证订单数和在租台数
3. **1.11 查询有效的IMEI编码** - 批量查询IMEI编码是否重复
4. **1.7 推送设备IMEI编码** - 推送设备IMEI编码到系统

每个功能都提供**基础版本**和**手动输入版本**，共8个可执行程序。

## 🔧 环境要求

- **Python**: 3.7+
- **依赖库**: requests, colorama

## 🚀 快速开始

### 1. 安装依赖
```bash
# 方式一：使用pip直接安装
pip install requests colorama

# 方式二：使用requirements.txt
pip install -r requirements.txt

# 方式三：使用setup.py
pip install -e .
```

### 2. 运行方式

#### 方式一：交互式菜单（推荐）
```bash
python main.py
```

#### 方式二：快速启动特定功能
```bash
# 结清公证订单
python run.py settlement-manual

# 用户有效订单查询
python run.py order-query-manual

# 查询有效的IMEI编码
python run.py imei-query-manual

# 推送设备IMEI编码
python run.py imei-push-manual
```

#### 方式三：直接运行模块
```bash
# 结清公证订单
python -m gz_api.early_settlement_order manual

# 用户有效订单查询
python -m gz_api.all_running_order_query manual

# 查询有效的IMEI编码
python -m gz_api.query_valid_imei manual

# 推送设备IMEI编码
python -m gz_api.imei_code_push manual
```

## 📁 项目结构

```
gz-api-python/
├── gz_api/                          # 主包
│   ├── __init__.py                  # 包初始化
│   ├── utils.py                     # 工具类（签名生成、HTTP请求等）
│   ├── response_parser.py           # 响应解析器
│   ├── early_settlement_order.py    # 结清公证订单
│   ├── all_running_order_query.py   # 用户有效订单查询
│   ├── query_valid_imei.py          # 查询有效的IMEI编码
│   └── imei_code_push.py            # 推送设备IMEI编码
├── main.py                          # 交互式菜单主程序
├── run.py                           # 快速启动脚本
├── requirements.txt                 # 依赖配置
├── setup.py                         # 安装配置
└── README.md                        # 使用说明
```

## 🎯 功能特性

### 核心功能
- ✅ **完整的参数验证**: 身份证号、IMEI格式验证
- ✅ **MD5签名生成**: 自动生成接口签名
- ✅ **HTTP请求处理**: 使用requests库
- ✅ **JSON处理**: 内置json模块
- ✅ **错误处理**: 完善的异常处理机制

### 用户体验
- ✅ **彩色输出**: 使用colorama库增强可读性
- ✅ **交互式输入**: 支持手动输入和默认值
- ✅ **参数验证**: 实时验证输入格式
- ✅ **确认机制**: 重要操作需要确认
- ✅ **友好提示**: 详细的操作说明和错误提示

### 安全特性
- ✅ **二次确认**: 结清订单等重要操作需要二次确认
- ✅ **参数验证**: 严格的输入格式验证
- ✅ **签名验证**: MD5签名确保请求安全
- ✅ **错误处理**: 完整的异常捕获和处理

## 📊 配置信息

```python
# 商户配置
MERCHANT_NO = "NJSJ1738988721355510"
SECRET_KEY = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk"
BASE_URL = "http://buss.haochengda.net:8088"
```

## 📝 使用示例

### 1. 交互式菜单
```bash
$ python main.py

============================================================
🎯 公证接口Python实现 - 功能菜单
============================================================
📋 商户信息:
   商户号: NJSJ1738988721355510
   服务器: http://buss.haochengda.net:8088

🚀 可用功能:
   1. 结清公证订单 (基础版本)
   2. 结清公证订单 (手动输入版本)
   3. 用户有效订单查询 (基础版本)
   4. 用户有效订单查询 (手动输入版本)
   5. 查询有效的IMEI编码 (基础版本)
   6. 查询有效的IMEI编码 (手动输入版本)
   7. 推送设备IMEI编码 (基础版本)
   8. 推送设备IMEI编码 (手动输入版本)
   0. 退出程序
============================================================

请选择功能 (0-8):
```

### 2. 结清公证订单示例
```bash
$ python run.py settlement-manual

=== 结清公证订单 - 手动输入模式 ===
商户号: NJSJ1738988721355510
功能说明: 结清指定的公证订单，操作不可逆
⚠️  注意: 结清后订单将无法再进行相关操作

请输入要结清的订单号（三方流水号） [默认: OI1970412769182351360]: 

=== 请求信息 ===
订单号: OI1970412769182351360
请求参数: {'gzOrderId': 'OI1970412769182351360'}

⚠️  重要提醒:
   - 结清操作不可逆
   - 结清后订单将无法再进行相关操作
   - 请确认订单号正确

确认要结清订单 OI1970412769182351360 吗？ (y/n): y
请再次确认结清操作 (输入 'CONFIRM' 继续): CONFIRM
```

### 3. IMEI查询示例
```bash
$ python run.py imei-query-manual

=== 查询有效的IMEI编码 - 手动输入模式 ===
商户号: NJSJ1738988721355510
功能说明: 批量查询IMEI编码是否重复，支持多个IMEI同时查询

请输入要查询的IMEI编码（支持多个，用逗号分隔）:
IMEI编码 [默认: 123456789012345,355011479595868]: 355011479595868,123456789012345
✅ 355011479595868 - 格式正确
✅ 123456789012345 - 格式正确

=== 请求信息 ===
查询的IMEI编码: 355011479595868, 123456789012345
请求参数: {'imeiCodes': ['355011479595868', '123456789012345']}

是否发送请求？ (y/n): y
```

## 🎨 格式化输出

### 成功响应示例
```
📱 IMEI查询结果:
   查询的IMEI数量: 2 个
   重复的IMEI数量: 0 个

🔍 验证结果:
   ✅ 所有IMEI编码都可用
   ✅ 可以继续申请公证

✅ 可用的IMEI编码:
   ✅ 355011479595868 (可用)
   ✅ 123456789012345 (可用)
```

### 错误处理示例
```
❌ 接口暂时不可用 (404错误)

💡 可能原因:
   1. 接口尚未在服务器端实现
   2. 接口路径可能不正确
   3. 该功能可能需要特殊权限
```

## 🔧 开发说明

### 添加新功能
1. 在`gz_api/`目录下创建新的Python文件
2. 使用`GzApiUtils.send_request()`发送HTTP请求
3. 使用`ResponseParser`解析响应结果
4. 在`main.py`和`run.py`中添加菜单项

### 自定义配置
修改`gz_api/utils.py`中的配置常量：
```python
class GzApiUtils:
    MERCHANT_NO = "你的商户号"
    SECRET_KEY = "你的密钥"
    BASE_URL = "你的服务器地址"
```

## 🆚 与Java版本对比

| 特性 | Python版本 | Java版本 |
|------|------------|----------|
| 功能完整性 | ✅ 100%相同 | ✅ 完整 |
| 参数验证 | ✅ 相同逻辑 | ✅ 完整 |
| 错误处理 | ✅ 相同机制 | ✅ 完整 |
| 格式化输出 | ✅ 彩色增强 | ✅ 基础格式 |
| 交互体验 | ✅ 更友好 | ✅ 标准 |
| 部署便利性 | ✅ 更简单 | ⚠️ 需要JVM |

## 🎉 总结

这是一个**功能完整、质量优秀**的公证接口Python实现：

- 🎯 **4个核心接口**全部实现
- 🎯 **8个可执行程序**（每个接口2个版本）
- 🎯 **完整的安全机制**（二次确认、参数验证）
- 🎯 **优秀的用户体验**（彩色输出、友好提示）
- 🎯 **简单的部署方式**（仅需Python环境）

推荐使用手动输入版本进行测试，体验完整的交互式操作！🚀
