#!/usr/bin/env python3
"""
1.9 结清公证订单
用于结清指定的公证订单
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gz_api.utils import GzApiUtils
from gz_api.response_parser import ResponseParser

def early_settlement_order_basic():
    """结清公证订单 - 基础版本"""
    # 构建请求参数
    params = {
        'gzOrderId': 'OI1970412769182351360'  # 订单号（三方流水号，唯一）
    }
    
    GzApiUtils.print_section("结清公证订单")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print(f"订单号: {params['gzOrderId']}")
    print(f"请求参数: {params}")
    
    # 发送请求
    response_data = GzApiUtils.send_request('gzs/earlySettlementOrder', params)
    
    # 解析响应
    ResponseParser.parse_settlement_response(response_data)

def early_settlement_order_manual():
    """结清公证订单 - 手动输入版本"""
    GzApiUtils.print_section("结清公证订单 - 手动输入模式")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print("功能说明: 结清指定的公证订单，操作不可逆")
    GzApiUtils.print_warning("注意: 结清后订单将无法再进行相关操作")
    print()
    
    try:
        # 获取用户输入
        gz_order_id = GzApiUtils.get_input_with_default(
            "请输入要结清的订单号（三方流水号）", 
            "OI1970412769182351360"
        )
        
        # 构建请求参数
        params = {
            'gzOrderId': gz_order_id
        }
        
        GzApiUtils.print_section("请求信息")
        print(f"订单号: {gz_order_id}")
        print(f"请求参数: {params}")
        
        # 二次确认
        print()
        GzApiUtils.print_warning("重要提醒:")
        print("   - 结清操作不可逆")
        print("   - 结清后订单将无法再进行相关操作")
        print("   - 请确认订单号正确")
        
        if GzApiUtils.confirm_action(f"\n确认要结清订单 {gz_order_id} 吗？"):
            final_confirm = input("请再次确认结清操作 (输入 'CONFIRM' 继续): ").strip()
            
            if final_confirm == 'CONFIRM':
                GzApiUtils.print_section("发送请求")
                
                # 发送请求
                response_data = GzApiUtils.send_request('gzs/earlySettlementOrder', params)
                
                # 解析响应
                ResponseParser.parse_settlement_response(response_data)
            else:
                GzApiUtils.print_error("确认失败，已取消结清操作")
        else:
            print("已取消结清操作")
            
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        GzApiUtils.print_error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "manual":
        early_settlement_order_manual()
    else:
        early_settlement_order_basic()
